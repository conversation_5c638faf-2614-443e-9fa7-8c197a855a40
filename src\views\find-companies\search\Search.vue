<!-- 找企业首页 -->
<template>
    <div class="display-flex flex-column height-100 search-index-box"
        :style="{ backgroundImage: 'url(' + searchCompanyBackUrl + ')' }">
        <div class="all-padding-16">
            <div class="color-gradinent-blue font-big-title">找企业</div>
            <div class="t-margin-36 display-flex width-100 gap-16 border-radius-4 flex-wrap">
                <div :class="searchActiveTab.val == item.val ? 'back-color-white box-shadow color-black' : 'color-two-grey'"
                    v-for="item in searchTabs" :key="item.val"
                    class="search-tab-item font-second-title tb-padding-4 lr-padding-8 pointer text-center border-radius-4"
                    @click="searchActiveTab = item">
                    {{ item.label }}
                </div>
            </div>
            <div class="search-input t-margin-16 display-flex">
                <div class="search-input-item">
                    <el-popover :visible="searchInputShow" :width="'45.5%'" ref="popoverRef" placement="bottom">
                        <template #reference>
                            <div class="search-box display-flex">
                                <div class="input-box display-flex top-bottom-center" ref="searchRef">
                                    <el-input :prefix-icon="Search" ref="searchInputRef" v-model="searchKey"
                                        :placeholder="searchActiveTab.plac" @keyup.enter="toComSearch"></el-input>
                                </div>
                                <view
                                    class="pointer color-blue lr-padding-12 display-flex top-bottom-center font-weight-400"
                                    @click="toComSearch">
                                    普通搜索
                                </view>
                            </div>
                        </template>
                        <div class="el-autocomplete-suggestion" v-loading="searchLoading"
                            v-click-outside="onClickOutside">

                            <div class="display-flex all-padding-8 top-bottom-center gap-8 pointer company-item border-radius-4"
                                v-for="item in searchResList" :key="item.name" @click="showCompanyDetail(item)">
                                <div class="logo-box border-radius-4 text-center color-blue">
                                    {{ getShortName(item.companyName) }}

                                </div>
                                <div class="flex-1">
                                    <div class="font-16 color-black" v-html="item.companyname_ws || item.companyName">
                                    </div>
                                    <div class="font-14 color-two-grey">{{ item.address }}</div>
                                </div>
                                <div class="tag">公司</div>
                            </div>

                        </div>
                    </el-popover>
                </div>
                <div class="pointer l-margin-24  search-btn box-shadow font-16 back-color-white font-weight-500 all-padding-7 border-radius-12 pointer"
                    @mouseover="highHover = true" @mouseleave="highHover = false" @click="toHighSearch">
                    <div class="display-flex top-bottom-center">
                        <Icon icon="icon-a-huaban273" :color="highColor" class="r-margin-5" /> 高级搜索
                    </div>
                    <div class="font-12 font-weight-400 t-margin-5 color-three-grey"
                        :class="highHover ? 'color-white' : ''">
                        条件组合精确定位
                    </div>
                </div>
                <div v-if="permissionService.isRecentRegPermitted()"
                    class="pointer l-margin-24 search-btn box-shadow font-16 back-color-white font-weight-500 all-padding-7 border-radius-12"
                    @mouseover="newRegHover = true" @mouseleave="newRegHover = false"
                    @click="jumpToRoute('recent-search')">
                    <div class="display-flex top-bottom-center">
                        <Icon icon="icon-a-huaban2671" :color="newRegColor" class="r-margin-5" /> 最新注册
                    </div>
                    <div class="font-12 font-weight-400 t-margin-5 color-three-grey"
                        :class="newRegHover ? 'color-white' : ''">
                        近一年内注册企业
                    </div>
                </div>
                <div v-if="permissionService.isSearchBidPermitted()"
                    class="pointer l-margin-24 search-btn box-shadow font-16 back-color-white font-weight-500 all-padding-7 border-radius-12"
                    @mouseover="tbHover = true" @mouseleave="tbHover = false" @click="jumpToRoute('bid-search')">
                    <div class="display-flex top-bottom-center">
                        <Icon icon="icon-a-huaban31" :color="tbColor" class="r-margin-5" /> 搜招投标
                    </div>
                    <div class="font-12 font-weight-400 t-margin-5 color-three-grey"
                        :class="tbHover ? 'color-white' : ''">
                        搜有招标投标信息企业
                    </div>
                </div>
                <div v-if="permissionService.isSearchFactoryPermitted()"
                    class="pointer l-margin-24 search-btn box-shadow font-16 back-color-white font-weight-500 all-padding-7 border-radius-12"
                    @mouseover="gcHover = true" @mouseleave="gcHover = false" @click="jumpToRoute('factory-search')">
                    <div class="display-flex top-bottom-center">
                        <Icon icon="icon-a-huaban30" :color="gcColor" class="r-margin-5" />搜工厂
                    </div>
                    <div class="font-12 font-weight-400 t-margin-5 color-three-grey"
                        :class="gcHover ? 'color-white' : ''">
                        搜生产加工类企业
                    </div>
                </div>
            </div>
        </div>
        <div class="lr-padding-16 flex-grow-1">
            <div class="back-color-white lr-padding-16 height-100">
                <el-tabs v-model="tabActive">
                    <el-tab-pane class="height-100" label="产业市场" name="industryMarket">
                        <IndustryMarket @useMarket="useMarket" />
                    </el-tab-pane>
                    <el-tab-pane class="height-100" label="数据图谱" name="dataChart">
                        <DataChartModel />
                    </el-tab-pane>
                    <el-tab-pane class="height-100" label="共享模板" name="shareTemplate">
                        <ShareHightSearchModel />
                    </el-tab-pane>
                    <el-tab-pane class="height-100" label="已保存的模板" name="saveModels">
                        <SaveHighSearchModel />
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { getCurrentInstance, ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import type { Ref } from 'vue'
import { ClickOutside as vClickOutside } from 'element-plus'
import type { ISearchCompanyItem, ISearchGetTemplateItem } from '@/types/company'
import IndustryMarket from '../../../components/search/IndustryMarket.vue'
import SaveHighSearchModel from './components/SaveHighSearchModel.vue'
import ShareHightSearchModel from '@/views/find-companies/search/components/ShareHightSearchModel.vue'
import DataChartModel from './components/DataChart.vue'
import Icon from '@/components/common/Icon.vue'
import { Search } from '@element-plus/icons-vue'
import aicService from '@/service/aicService'
import permissionService from '@/service/permissionService'
import searchCompanyBackUrl from '@/assets/images/search-company.png'

const instance = getCurrentInstance()
const router = useRouter()

const searchTabs = instance?.appContext.config.globalProperties.$commom.commonData.searchTagTypes

const tbHover = ref(false)
const tbColor = computed(() => (!tbHover.value ? '#4EDA55' : '#fff'))
const newRegHover = ref(false)
const newRegColor = computed(() => (!newRegHover.value ? 'red' : '#fff'))
const highColor = computed(() => (!highHover.value ? '#1966FF' : '#fff'))
const highHover = ref(false)
const gcHover = ref(false)
const gcColor = computed(() => (!gcHover.value ? '#D99A4E' : '#fff'))

const searchActiveTab = ref(searchTabs[0])
const searchKey = ref('')
const tabActive = ref('industryMarket')

const searchInputRef = ref()

const searchInputShow: Ref<boolean> = ref(false)
const searchLoading: Ref<boolean> = ref(false)
const searchResList: Ref<ISearchCompanyItem[]> = ref([])

const toComSearch = () => {
    router.push({
        name: 'com-search-company',
        params: {
            tagTypeVal: searchActiveTab.value.val,
            keyword: searchKey.value,
        },
    })
}

const jumpToRoute = (name: string) => {
    router.push({
        name
    })
}

const toHighSearch = () => {
    router.push({
        name: 'more-search-company',
        params: {},
    })
}

let tm: number | null = null
watch(searchKey, (nVal) => {
    if (tm) {
        clearTimeout(tm)
    }
    if (!nVal.length) {
        searchLoading.value = false
        searchInputShow.value = false
        return
    }
    tm = window.setTimeout(() => {
        searchInputShow.value = true
        searchLoading.value = true

        aicService.searchEnterprise({
            keyword: nVal,
            scope: searchActiveTab.value.val,
            pageSize: 10,
            page: 1
        }).then((res) => {
            console.log(res)
            searchResList.value = res.data
            searchLoading.value = false
        }).finally(() => { })
    }, 500)


})


const showCompanyDetail = (item: ISearchCompanyItem) => {



    if (window.self === window.top) {
        const routeUrl = router.resolve({ name: 'company-profile', params: { socialCreditCode: item.socialCreditCode } }).href
        window.open(routeUrl, '_blank')
    } else {
        router.push({
            name: 'company-profile',
            params: {
                socialCreditCode: item.socialCreditCode,
            },
        })
    }

}


const useMarket = (item: ISearchGetTemplateItem) => {

    router.push({
        name: 'more-search-company',
        params: {
            templeteId: item.id,
            type: 'market'
        },
    })
}

const getShortName = (str: string) => {
    const pattern = /([\u4e00-\u9fa5]{2,}省)/
    const res = str.replace(pattern, '')
    const provinces = [
        '北京', '天津', '河北', '山西', '辽宁', '吉林', '黑龙江',
        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东',
        '河南', '湖北', '湖南', '广东', '海南', '四川', '贵州',
        '云南', '陕西', '甘肃', '青海', '台湾', '内蒙古', '广西',
        '西藏', '宁夏', '新疆', '香港', '澳门', '重庆'
    ]

    // 按长度降序排序，确保优先匹配长名称
    provinces.sort((a, b) => b.length - a.length)

    // 构建正则表达式
    const regex = new RegExp(
        `^(${provinces.map(p =>
            // 处理特殊字符并添加边界匹配
            p.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
        ).join('|')})`
    )

    // 去除匹配到的省级行政区划
    const cleanedStr = res.replace(regex, '')

    const patterncity = /([\u4e00-\u9fa5]{2,}市)/
    const cleanedStringCity = cleanedStr.replace(patterncity, '')

    // 返回前四个字符
    return cleanedStringCity.slice(0, 4).replace(/[（）()]/g, '')
}

const onClickOutside = () => {
    searchInputShow.value = false
}
</script>

<style lang="scss" scoped>
.search-index-box {

    &:deep .el-tabs {
        height: 100%;

        &:deep .el-tab-pane {
            height: 100%;
        }
    }
}

.search-tab-item {
    // width: 72px;
}


.search-input-item {
    width: 52.5%;
}

::v-deep .search-input .el-input__wrapper {
    box-shadow: none;
}

.search-box {
    background: #fff;
    height: 100%;
    border-radius: 12px;
    box-shadow: 0px 4px 12px rgba(25, 102, 255, 0.16);

    .input-box {
        flex-grow: 1;
    }

    .el-input__wrapper:hover {
        border: 0;
    }
}

.search-btn {
    width: 140px;

    &:hover {
        background-color: var(--main-blue-);
        color: var(--main-white);
    }
}

.search-box:hover {
    box-shadow: 0px 0px 8px var(--main-blue-);
}

.high-search-btn {
    box-shadow: inset -2px -2px 12px #f2f7ff;
}

.high-search-btn:hover {
    background: #4d88ff;
}

.el-autocomplete-suggestion ul {
    padding: 0;
    min-height: 80px;
    max-height: 400px
}

.logo-box {
    width: 40px;
    height: 40px;
    background-color: var(--el-color-primary-light);
}

.company-item:hover {
    background-color: #F7F8FB;
}
</style>
